/**
 * 配置管理器 - 基于fount平台架构重新设计
 * 支持多AI源的统一配置管理
 */

import { loadJsonFileIfExists, saveJsonFile } from '../lib/json_loader.mjs'
import path from 'node:path'

/**
 * 默认配置模板
 */
const DEFAULT_CONFIG = {
	// 基础设置
	username: 'User',
	platform: 'shell',
	locale: 'zh-CN',
	debug: false,
	
	// 平台特定配置
	discord: {
		token: '',
		clientId: '',
		guildId: ''
	},
	telegram: {
		token: '',
		botUsername: '',
		ownerUserId: '',
		ownerUsername: '',
		ownerNameKeywords: ['主人', 'master'],
		enableGroupChat: true,
		enablePrivateChat: true,
		autoReplyToMentions: true,
		autoReplyToReplies: true
	},
	
	// AI源全局设置
	ai: {
		defaultSource: 'sfw',
		maxTokens: 4000,
		temperature: 0.7,
		topP: 0.9,
		frequencyPenalty: 0.0,
		presencePenalty: 0.0
	},
	
	// 内存管理设置
	memory: {
		shortTermEnabled: true,
		longTermEnabled: true,
		maxShortTermEntries: 1000,
		maxLongTermEntries: 10000,
		cleanupInterval: 86400000,
		memoryTTL: 31536000000
	},
	
	// 详细思考配置
	detail_thinking: {
		max_planning_cycles: 4,
		initial_plan_max_retries: 5,
		summary_max_retries: 5,
		thinking_interval: 3000
	}
}

/**
 * AI源配置模板
 */
const AI_SOURCE_TEMPLATES = {
	openai: {
		name: 'OpenAI兼容接口',
		type: 'openai',
		endpoint: 'https://api.openai.com/v1/chat/completions',
		apiKey: '',
		model: 'gpt-3.5-turbo',
		parameters: {
			temperature: 0.7,
			top_p: 0.9,
			max_tokens: 4000
		},
		enabled: false,
		description: 'OpenAI兼容的API接口'
	},
	claude: {
		name: 'Claude模型',
		type: 'claude',
		endpoint: 'https://api.anthropic.com/v1/messages',
		apiKey: '',
		model: 'claude-3-sonnet-20240229',
		parameters: {
			temperature: 0.7,
			top_p: 0.9,
			max_tokens: 4000
		},
		enabled: false,
		description: 'Anthropic Claude模型'
	},
	gemini: {
		name: 'Gemini模型',
		type: 'gemini',
		endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent',
		apiKey: '',
		model: 'gemini-1.5-flash',
		parameters: {
			temperature: 0.7,
			top_p: 0.9,
			max_tokens: 4000
		},
		enabled: false,
		description: 'Google Gemini AI模型'
	},
	local: {
		name: '本地模型',
		type: 'local',
		endpoint: 'http://localhost:11434',
		apiKey: '',
		model: 'llama2:7b',
		parameters: {
			temperature: 0.7,
			top_p: 0.9,
			repeat_penalty: 1.1
		},
		enabled: false,
		description: '本地部署的开源模型'
	}
}

/**
 * 配置管理器类
 */
class ConfigManager {
	constructor() {
		this.configDir = './config'
		this.mainConfigPath = path.join(this.configDir, 'main.json')
		this.aiSourcesConfigPath = path.join(this.configDir, 'ai_sources.json')
		this.callingOrderConfigPath = path.join(this.configDir, 'calling_order.json')
		
		this.config = null
		this.aiSources = null
		this.callingOrder = null
	}

	/**
	 * 初始化配置管理器
	 */
	async init() {
		await this.loadMainConfig()
		await this.loadAISourcesConfig()
		await this.loadCallingOrderConfig()
	}

	/**
	 * 加载主配置文件
	 */
	async loadMainConfig() {
		this.config = loadJsonFileIfExists(this.mainConfigPath, { ...DEFAULT_CONFIG })
		
		// 确保配置完整性
		this.config = { ...DEFAULT_CONFIG, ...this.config }
		
		// 保存配置以确保文件存在
		await this.saveMainConfig()
	}

	/**
	 * 保存主配置文件
	 */
	async saveMainConfig() {
		return saveJsonFile(this.mainConfigPath, this.config)
	}

	/**
	 * 加载AI源配置
	 */
	async loadAISourcesConfig() {
		this.aiSources = loadJsonFileIfExists(this.aiSourcesConfigPath, {})
		
		// 如果没有配置，创建默认的AI源配置
		if (Object.keys(this.aiSources).length === 0) {
			this.aiSources = {
				sfw: {
					...AI_SOURCE_TEMPLATES.openai,
					name: '日常对话模型',
					enabled: true,
					description: '用于日常对话的标准模型'
				},
				nsfw: {
					...AI_SOURCE_TEMPLATES.openai,
					name: '内容创作模型',
					enabled: false,
					description: '处理创意内容的专用模型'
				},
				expert: {
					...AI_SOURCE_TEMPLATES.claude,
					name: '专家咨询模型',
					enabled: false,
					description: '用于专业问题和技术咨询的高级模型'
				}
			}
		}
		
		await this.saveAISourcesConfig()
	}

	/**
	 * 保存AI源配置
	 */
	async saveAISourcesConfig() {
		return saveJsonFile(this.aiSourcesConfigPath, this.aiSources)
	}

	/**
	 * 加载调用顺序配置
	 */
	async loadCallingOrderConfig() {
		this.callingOrder = loadJsonFileIfExists(this.callingOrderConfigPath, {
			'detail-thinking': ['expert', 'sfw'],
			'web-browse': ['sfw'],
			'nsfw': ['nsfw', 'sfw'],
			'sfw': ['sfw'],
			'expert': ['expert', 'sfw'],
			'logic': ['sfw']
		})
		
		await this.saveCallingOrderConfig()
	}

	/**
	 * 保存调用顺序配置
	 */
	async saveCallingOrderConfig() {
		return saveJsonFile(this.callingOrderConfigPath, this.callingOrder)
	}

	/**
	 * 获取主配置
	 */
	getMainConfig() {
		return this.config
	}

	/**
	 * 更新主配置
	 */
	async updateMainConfig(newConfig) {
		this.config = { ...this.config, ...newConfig }
		return await this.saveMainConfig()
	}

	/**
	 * 获取AI源配置
	 */
	getAISourcesConfig() {
		return this.aiSources
	}

	/**
	 * 获取特定AI源配置
	 */
	getAISourceConfig(sourceName) {
		return this.aiSources[sourceName] || null
	}

	/**
	 * 更新AI源配置
	 */
	async updateAISourceConfig(sourceName, newConfig) {
		if (!this.aiSources[sourceName]) {
			this.aiSources[sourceName] = {}
		}
		this.aiSources[sourceName] = { ...this.aiSources[sourceName], ...newConfig }
		return await this.saveAISourcesConfig()
	}

	/**
	 * 添加新的AI源
	 */
	async addAISource(sourceName, config) {
		this.aiSources[sourceName] = config
		return await this.saveAISourcesConfig()
	}

	/**
	 * 删除AI源
	 */
	async removeAISource(sourceName) {
		delete this.aiSources[sourceName]
		return await this.saveAISourcesConfig()
	}

	/**
	 * 获取调用顺序配置
	 */
	getCallingOrderConfig() {
		return this.callingOrder
	}

	/**
	 * 更新调用顺序配置
	 */
	async updateCallingOrderConfig(newOrder) {
		this.callingOrder = { ...this.callingOrder, ...newOrder }
		return await this.saveCallingOrderConfig()
	}

	/**
	 * 获取AI源模板
	 */
	getAISourceTemplate(type) {
		return AI_SOURCE_TEMPLATES[type] || null
	}

	/**
	 * 获取所有AI源模板
	 */
	getAllAISourceTemplates() {
		return AI_SOURCE_TEMPLATES
	}
}

// 单例实例
let configManagerInstance = null

/**
 * 获取配置管理器实例
 */
export function getConfigManager() {
	if (!configManagerInstance) {
		configManagerInstance = new ConfigManager()
	}
	return configManagerInstance
}

/**
 * 初始化配置系统
 */
export async function initConfig() {
	const manager = getConfigManager()
	await manager.init()
	return manager
}

export { ConfigManager }
