/**
 * 字符串转义工具函数 - 兼容fount平台实现
 * 用于正则表达式和其他字符串处理
 */

/**
 * 转义正则表达式特殊字符 (fount平台兼容版本)
 * @param {string} string - 需要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeRegExp(string) {
	return string.replace(/[$()*+./?[\\-^{|}]/g, '\\$&')
}

/**
 * 反转义正则表达式字符
 * @param {string} string - 需要反转义的字符串
 * @returns {string} 反转义后的字符串
 */
export function unescapeRegExp(string) {
	return string.replace(/\\(.)/g, '$1')
}

/**
 * 将Unicode转义序列转换为字符
 * @param {string} str - 包含Unicode转义序列的字符串
 * @returns {string} 转换后的字符串
 */
export function unicodeEscapeToChar(str) {
	return str.replace(/\\u[\dA-Fa-f]{4}/g, match => String.fromCharCode(parseInt(match.replace('\\u', ''), 16)))
}

/**
 * 反转义Unicode字符
 * @param {string} str - 包含Unicode转义序列的字符串
 * @returns {string} 转换后的字符串
 */
export function unescapeUnicode(str) {
	if (!(Object(str) instanceof String)) str = str.toString()
	return str.replace(/\\u([\da-f]{4})/gi, (match, p1) => String.fromCharCode(parseInt(p1, 16)))
}

/**
 * 转义HTML特殊字符
 * @param {string} string - 需要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeHtml(string) {
	if (typeof string !== 'string') {
		return ''
	}
	const htmlEscapes = {
		'&': '&amp;',
		'<': '&lt;',
		'>': '&gt;',
		'"': '&quot;',
		"'": '&#39;'
	}
	return string.replace(/[&<>"']/g, (match) => htmlEscapes[match])
}

/**
 * 转义JSON字符串
 * @param {string} string - 需要转义的字符串
 * @returns {string} 转义后的字符串
 */
export function escapeJson(string) {
	if (typeof string !== 'string') {
		return ''
	}
	return string.replace(/\\/g, '\\\\')
		.replace(/"/g, '\\"')
		.replace(/\n/g, '\\n')
		.replace(/\r/g, '\\r')
		.replace(/\t/g, '\\t')
}

/**
 * 反转义字符串
 * @param {string} string - 需要反转义的字符串
 * @returns {string} 反转义后的字符串
 */
export function unescapeString(string) {
	if (typeof string !== 'string') {
		return ''
	}
	return string.replace(/\\(.)/g, '$1')
}
