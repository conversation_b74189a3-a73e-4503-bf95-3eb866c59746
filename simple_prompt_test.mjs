/**
 * 简单的 prompt 构建测试
 */

async function testPromptBuilding() {
    console.log('🧪 开始测试 prompt 构建...')
    
    try {
        // 导入 GetPrompt 函数
        const { GetPrompt } = await import('./prompt/index.mjs')
        
        // 创建测试参数
        const args = {
            char_id: 'gentian',
            UserCharname: '测试用户',
            ReplyToCharname: '测试用户',
            Charname: '龙胆',
            chat_log: [
                {
                    name: '测试用户',
                    role: 'user',
                    content: '你好，龙胆！'
                },
                {
                    name: '龙胆',
                    role: 'assistant',
                    content: '你好！我是龙胆·阿芙萝黛蒂，很高兴见到你！'
                },
                {
                    name: '测试用户',
                    role: 'user',
                    content: '请帮我写一个简单的程序'
                }
            ],
            char: null,
            user: null,
            world: null,
            other_chars: {},
            plugins: {},
            locales: ['zh-CN']
        }
        
        console.log('📝 测试参数:', {
            char_id: args.char_id,
            UserCharname: args.UserCharname,
            Charname: args.Charname,
            chat_log_length: args.chat_log.length
        })
        
        // 调用 GetPrompt
        console.log('\n🔄 调用 GetPrompt...')
        const prompt = await GetPrompt(args, 3)
        
        console.log('\n✅ 成功构建 prompt!')
        console.log(`📊 prompt 结构:`, {
            text_count: prompt.text?.length || 0,
            additional_chat_log_count: prompt.additional_chat_log?.length || 0,
            extension_keys: Object.keys(prompt.extension || {})
        })
        
        // 显示部分内容
        if (prompt.text && prompt.text.length > 0) {
            console.log(`\n📋 prompt 文本内容 (前3条):`)
            prompt.text.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. [重要度: ${item.important}] ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}`)
            })
        }
        
        if (prompt.additional_chat_log && prompt.additional_chat_log.length > 0) {
            console.log(`\n💬 额外聊天日志 (前3条):`)
            prompt.additional_chat_log.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. [${item.role}] ${item.name}: ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}`)
            })
        }
        
        console.log('\n🎉 prompt 构建测试完成!')
        return true
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message)
        console.error('错误堆栈:', error.stack)
        return false
    }
}

// 运行测试
if (import.meta.main) {
    testPromptBuilding()
        .then(success => {
            if (success) {
                console.log('\n🎯 prompt 构建测试通过!')
                process.exit(0)
            } else {
                console.log('\n💥 prompt 构建测试失败!')
                process.exit(1)
            }
        })
        .catch(error => {
            console.error('\n💥 测试运行出错:', error)
            process.exit(1)
        })
}

export { testPromptBuilding }
