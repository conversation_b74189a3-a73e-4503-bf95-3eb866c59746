#!/usr/bin/env deno run --allow-all
/**
 * GentianAphrodite 独立应用程序入口
 * 脱离 fount 平台的独立运行版本
 */

import { parseArgs } from 'node:util'
import { existsSync } from 'node:fs'
import { join } from 'node:path'

// 导入核心模块
import GentianAphrodite from './main.mjs'
import { initCharBase } from './charbase.mjs'
import { initTranslations } from './lib/i18n.mjs'
import { initLocale } from './lib/locale.mjs'
import { getConfigManager } from './config/config_manager.mjs'

/**
 * 应用程序配置
 */
const APP_CONFIG = {
	name: 'GentianAphrodite',
	version: '1.0.0',
	description: '龙胆 - 独立 AI 角色应用程序',
	author: 'steve02081504',
	dataDir: './data',
	configDir: './config',
	logsDir: './logs'
}

/**
 * 命令行选项定义
 */
const CLI_OPTIONS = {
	help: {
		type: 'boolean',
		short: 'h',
		description: '显示帮助信息'
	},
	version: {
		type: 'boolean',
		short: 'v',
		description: '显示版本信息'
	},
	config: {
		type: 'string',
		short: 'c',
		description: '指定配置文件路径'
	},
	platform: {
		type: 'string',
		short: 'p',
		description: '指定运行平台 (discord, telegram, shell)',
		default: 'shell'
	},
	locale: {
		type: 'string',
		short: 'l',
		description: '指定语言 (zh-CN, en-US, ja-JP, ko-KR)',
		default: 'zh-CN'
	},
	username: {
		type: 'string',
		short: 'u',
		description: '指定用户名',
		default: 'User'
	},
	debug: {
		type: 'boolean',
		short: 'd',
		description: '启用调试模式'
	}
}

/**
 * 显示帮助信息
 */
function showHelp() {
	console.log(`${APP_CONFIG.name} v${APP_CONFIG.version}`)
	console.log(APP_CONFIG.description)
	console.log(`作者: ${APP_CONFIG.author}`)
	console.log('')
	console.log('用法:')
	console.log('  deno run --allow-all app.mjs [选项]')
	console.log('')
	console.log('选项:')
	
	for (const [name, option] of Object.entries(CLI_OPTIONS)) {
		const shortFlag = option.short ? `-${option.short}, ` : '    '
		const longFlag = `--${name}`
		const defaultValue = option.default ? ` (默认: ${option.default})` : ''
		console.log(`  ${shortFlag}${longFlag.padEnd(15)} ${option.description}${defaultValue}`)
	}
	
	console.log('')
	console.log('示例:')
	console.log('  deno run --allow-all app.mjs --platform discord --username Alice')
	console.log('  deno run --allow-all app.mjs --platform telegram --config ./my-config.json')
	console.log('  deno run --allow-all app.mjs --platform shell --debug')
}

/**
 * 显示版本信息
 */
function showVersion() {
	console.log(`${APP_CONFIG.name} v${APP_CONFIG.version}`)
}

/**
 * 初始化应用程序目录
 */
function initDirectories() {
	const dirs = [APP_CONFIG.dataDir, APP_CONFIG.configDir, APP_CONFIG.logsDir]
	
	for (const dir of dirs) {
		if (!existsSync(dir)) {
			try {
				Deno.mkdirSync(dir, { recursive: true })
				console.log(`创建目录: ${dir}`)
			} catch (error) {
				console.error(`创建目录失败 ${dir}:`, error.message)
				Deno.exit(1)
			}
		}
	}
}

/**
 * 加载配置文件 (使用新配置系统)
 * @param {string} configPath - 配置文件路径 (兼容性参数，实际使用新配置系统)
 * @returns {Promise<Object>} 配置对象
 */
async function loadConfig(configPath) {
	try {
		const configManager = getConfigManager()
		await configManager.init()

		// 获取主配置
		const mainConfig = configManager.getMainConfig()

		// 如果指定了旧的配置文件路径且存在，则合并配置
		if (configPath && existsSync(configPath)) {
			try {
				const configText = Deno.readTextFileSync(configPath)
				const legacyConfig = JSON.parse(configText)
				console.log(`📄 检测到旧配置文件 ${configPath}，正在合并配置...`)

				// 合并配置并更新到新系统
				const mergedConfig = { ...mainConfig, ...legacyConfig }
				await configManager.updateMainConfig(mergedConfig)

				return mergedConfig
			} catch (error) {
				console.warn(`加载旧配置文件失败 ${configPath}:`, error.message)
			}
		}

		return mainConfig
	} catch (error) {
		console.error('加载新配置系统失败:', error.message)
		// 返回默认配置作为后备
		return {
			username: 'User',
			platform: 'shell',
			locale: 'zh-CN',
			debug: false,
			discord: { token: '', clientId: '', guildId: '' },
			telegram: { token: '', botUsername: '' },
			ai: { defaultSource: 'sfw', maxTokens: 4000, temperature: 0.7 }
		}
	}
}

/**
 * 初始化应用程序
 * @param {Object} options - 初始化选项
 */
async function initApp(options) {
	const { username, locale, debug, config } = options

	// 设置调试模式
	if (debug) {
		console.log('调试模式已启用')
		globalThis.DEBUG = true
	}

	// 初始化目录
	initDirectories()

	// 初始化本地化
	initLocale(locale)
	initTranslations('default', locale)

	// 初始化角色基础信息
	const initStat = {
		username,
		config,
		dataDir: APP_CONFIG.dataDir
	}

	await initCharBase(initStat)

	// 初始化角色 API
	if (GentianAphrodite.Init) {
		await GentianAphrodite.Init(initStat)
	}

	if (GentianAphrodite.Load) {
		await GentianAphrodite.Load(initStat)
	}

	console.log(`${APP_CONFIG.name} 初始化完成`)
	console.log(`用户: ${username}`)
	console.log(`语言: ${locale}`)
	console.log(`平台: ${options.platform}`)
}

/**
 * 启动指定平台
 * @param {string} platform - 平台名称
 * @param {Object} config - 配置对象
 */
async function startPlatform(platform, config) {
	switch (platform) {
		case 'discord':
			console.log('启动 Discord 平台...')
			// TODO: 实现 Discord 平台启动逻辑
			console.warn('Discord 平台尚未实现')
			break

		case 'telegram':
			console.log('启动 Telegram 平台...')
			await startTelegramBot(config)
			break

		case 'shell':
			console.log('启动 Shell 交互模式...')
			await startShellMode(config)
			break

		default:
			console.error(`不支持的平台: ${platform}`)
			Deno.exit(1)
	}
}

/**
 * 启动 Telegram 机器人
 * @param {Object} config - 配置对象
 */
async function startTelegramBot(config) {
	const telegramConfig = config.telegram

	if (!telegramConfig || !telegramConfig.token || telegramConfig.token === 'YOUR_BOT_TOKEN_HERE') {
		console.error('❌ Telegram 配置错误：缺少有效的机器人令牌')
		console.log('')
		console.log('📋 请按照以下步骤配置 Telegram 机器人：')
		console.log('1. 在 Telegram 中联系 @BotFather 创建机器人')
		console.log('2. 获取机器人令牌 (Bot Token)')
		console.log('3. 编辑 config/main.json 文件，设置以下字段：')
		console.log('   - telegram.token: "你的机器人令牌"')
		console.log('   - telegram.ownerUserId: "你的Telegram用户ID"')
		console.log('   - telegram.ownerUsername: "你的Telegram用户名"')
		console.log('')
		console.log('💡 详细设置指南请参考 TELEGRAM-SETUP.md 文件')
		console.log('')
		Deno.exit(1)
	}

	try {
		// 动态导入 Telegraf
		const { Telegraf } = await import('npm:telegraf')

		// 创建机器人实例
		const bot = new Telegraf(telegramConfig.token)

		// 获取机器人配置模板并合并用户配置
		const configTemplate = await GentianAphrodite.interfaces.telegram.GetBotConfigTemplate()
		const botConfig = {
			...configTemplate,
			...telegramConfig,
			OwnerUserID: telegramConfig.ownerUserId || configTemplate.OwnerUserID,
			OwnerUserName: telegramConfig.ownerUsername || configTemplate.OwnerUserName,
			OwnerNameKeywords: telegramConfig.ownerNameKeywords || configTemplate.OwnerNameKeywords
		}

		console.log(`Telegram 机器人配置:`)
		console.log(`- 机器人令牌: ${telegramConfig.token.substring(0, 10)}...`)
		console.log(`- 主人用户ID: ${botConfig.OwnerUserID}`)
		console.log(`- 主人用户名: ${botConfig.OwnerUserName}`)

		// 启动机器人
		await GentianAphrodite.interfaces.telegram.BotSetup(bot, botConfig)

		// 启动轮询
		console.log('正在启动 Telegram 机器人...')
		await bot.launch()

		console.log('✅ Telegram 机器人已成功启动！')
		console.log('机器人现在可以接收和回复消息了。')
		console.log('按 Ctrl+C 停止机器人。')

		// 优雅关闭处理
		const gracefulShutdown = async (signal) => {
			console.log(`\n收到 ${signal} 信号，正在关闭 Telegram 机器人...`)
			try {
				bot.stop(signal)
				console.log('Telegram 机器人已停止')
			} catch (error) {
				console.error('停止机器人时出错:', error.message)
			}
		}

		// 注册信号处理器
		Deno.addSignalListener('SIGINT', () => gracefulShutdown('SIGINT'))
		Deno.addSignalListener('SIGTERM', () => gracefulShutdown('SIGTERM'))

	} catch (error) {
		console.error('启动 Telegram 机器人失败:', error.message)
		if (error.message.includes('401')) {
			console.error('错误：机器人令牌无效。请检查配置文件中的 telegram.token 设置。')
		} else if (error.message.includes('network')) {
			console.error('错误：网络连接失败。请检查网络连接和防火墙设置。')
		}
		Deno.exit(1)
	}
}

/**
 * 启动 Shell 交互模式
 * @param {Object} config - 配置对象
 */
async function startShellMode(config) {
	console.log('进入交互模式，输入 "exit" 或 "quit" 退出')
	console.log('输入 "help" 查看可用命令')
	console.log('')

	while (true) {
		try {
			// 读取用户输入
			const input = prompt('> ')
			
			if (!input) continue

			const trimmedInput = input.trim()

			// 处理特殊命令
			if (trimmedInput === 'exit' || trimmedInput === 'quit') {
				console.log('再见！')
				break
			}

			if (trimmedInput === 'help') {
				console.log('可用命令:')
				console.log('  help  - 显示此帮助信息')
				console.log('  exit  - 退出程序')
				console.log('  quit  - 退出程序')
				console.log('  其他输入将作为消息发送给 AI')
				continue
			}

			// TODO: 处理用户消息并获取 AI 回复
			console.log('AI 回复功能尚未实现')
			console.log(`您说: ${trimmedInput}`)

		} catch (error) {
			console.error('处理输入时出错:', error.message)
		}
	}
}

/**
 * 应用程序清理
 */
async function cleanup() {
	console.log('正在清理资源...')
	
	try {
		if (GentianAphrodite.Unload) {
			await GentianAphrodite.Unload('shutdown')
		}
		console.log('清理完成')
	} catch (error) {
		console.error('清理时出错:', error.message)
	}
}

/**
 * 主函数
 */
async function main() {
	try {
		// 解析命令行参数
		const { values: args } = parseArgs({
			args: Deno.args,
			options: CLI_OPTIONS,
			allowPositionals: false
		})

		// 处理帮助和版本选项
		if (args.help) {
			showHelp()
			return
		}

		if (args.version) {
			showVersion()
			return
		}

		// 加载配置（默认使用 config.json）
		const configPath = args.config || './config.json'
		const config = await loadConfig(configPath)

		// 合并命令行参数和配置文件
		const options = {
			username: args.username || config.username,
			platform: args.platform || config.platform,
			locale: args.locale || config.locale,
			debug: args.debug || config.debug,
			config
		}

		// 初始化应用程序
		await initApp(options)

		// 启动指定平台
		await startPlatform(options.platform, config)

	} catch (error) {
		console.error('应用程序启动失败:', error.message)
		if (globalThis.DEBUG) {
			console.error(error.stack)
		}
		Deno.exit(1)
	}
}

// 注册清理处理器
globalThis.addEventListener('beforeunload', cleanup)
globalThis.addEventListener('unload', cleanup)

// 处理 Ctrl+C
Deno.addSignalListener('SIGINT', async () => {
	console.log('\n收到中断信号，正在退出...')
	await cleanup()
	Deno.exit(0)
})

// 启动应用程序
if (import.meta.main) {
	main()
}
