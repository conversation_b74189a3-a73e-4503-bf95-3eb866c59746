/**
 * 配置系统兼容接口
 * 为现有代码提供向后兼容的配置访问接口
 */

import { getConfigManager } from './config/config_manager.mjs'

// 全局配置管理器实例
let configManager = null

/**
 * 初始化配置系统
 */
async function initConfigSystem() {
	if (!configManager) {
		const { initConfig } = await import('./config/config_manager.mjs')
		configManager = await initConfig()
	}
	return configManager
}

/**
 * 获取配置数据 (兼容现有接口)
 */
export async function GetData() {
	await initConfigSystem()
	
	const mainConfig = configManager.getMainConfig()
	const aiSources = configManager.getAISourcesConfig()
	const callingOrder = configManager.getCallingOrderConfig()
	
	// 转换为现有代码期望的格式
	const aiSourcesData = {}
	for (const [name, config] of Object.entries(aiSources)) {
		if (config.enabled) {
			aiSourcesData[name] = `${name}.json`
		}
	}
	
	return {
		// 主配置
		...mainConfig,
		
		// AI源数据 (兼容现有格式)
		AIsources: aiSourcesData,
		
		// 调用顺序
		callingOrder,
		
		// 详细思考配置
		detail_thinking: mainConfig.detail_thinking
	}
}

/**
 * 设置配置数据 (兼容现有接口)
 */
export async function SetData(data) {
	await initConfigSystem()
	
	// 更新主配置
	if (data.detail_thinking) {
		await configManager.updateMainConfig({ detail_thinking: data.detail_thinking })
	}
	
	// 更新其他主配置项
	const mainConfigUpdates = {}
	const excludeKeys = ['AIsources', 'callingOrder', 'detail_thinking']
	for (const [key, value] of Object.entries(data)) {
		if (!excludeKeys.includes(key)) {
			mainConfigUpdates[key] = value
		}
	}
	if (Object.keys(mainConfigUpdates).length > 0) {
		await configManager.updateMainConfig(mainConfigUpdates)
	}
	
	// 更新AI源配置
	if (data.AIsources) {
		// 这里需要处理AI源数据的更新
		// 现有代码传递的是 { sourceName: filename } 格式
		// 我们需要保持AI源配置的完整性
		console.log('AI源配置更新:', data.AIsources)
	}
	
	// 更新调用顺序
	if (data.callingOrder) {
		await configManager.updateCallingOrderConfig(data.callingOrder)
	}
}

/**
 * 获取AI源数据 (兼容现有接口)
 */
export function getAISourceData() {
	if (!configManager) {
		return {}
	}
	
	const aiSources = configManager.getAISourcesConfig()
	const result = {}
	
	for (const [name, config] of Object.entries(aiSources)) {
		if (config.enabled) {
			result[name] = `${name}.json`
		}
	}
	
	return result
}

/**
 * 设置AI源数据 (兼容现有接口)
 */
export async function setAISourceData(data) {
	await initConfigSystem()
	
	// 这个函数在现有代码中用于更新AI源映射
	// 我们需要保持兼容性，但实际的AI源配置管理由新系统处理
	console.log('设置AI源数据:', data)
}

/**
 * 获取配置管理器实例 (新接口)
 */
export async function getConfig() {
	await initConfigSystem()
	return configManager
}

/**
 * 导出兼容的config对象
 */
export const config = {
	detail_thinking: {
		max_planning_cycles: 4,
		initial_plan_max_retries: 5,
		summary_max_retries: 5,
		thinking_interval: 3000
	}
}

// 在模块加载时初始化配置系统
initConfigSystem().catch(console.error)
