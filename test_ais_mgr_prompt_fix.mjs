/**
 * 测试 AIS_Mgr prompt 构建修复
 */

// 直接导入类，因为 AIsources_manager.mjs 导出的是实例
import './lib/AIsources_manager.mjs'

// 创建一个简单的测试管理器类
class TestAISourcesManager {
    constructor() {
        this.sources = new Map()
        this.configDir = './config/aisources'
    }

    async convertStructToMessages(prompt_struct) {
        // 导入必要的模块
        const { structPromptToSingleNoChatLog, margeStructPromptChatLog } = await import('./lib/prompt_struct.mjs')
        const { GetPrompt } = await import('./prompt/index.mjs')

        const messages = []

        try {
            // 构建 args 参数，将 prompt_struct 转换为 GetPrompt 所需的格式
            const args = {
                char_id: prompt_struct.char_id || 'gentian',
                UserCharname: prompt_struct.UserCharname || '用户',
                ReplyToCharname: prompt_struct.ReplyToCharname,
                Charname: prompt_struct.Charname || '龙胆',
                chat_log: prompt_struct.chat_log || [],
                // 添加其他必要的参数
                char: null, // 角色对象，暂时为空
                user: null, // 用户对象，暂时为空
                world: null, // 世界对象，暂时为空
                other_chars: {}, // 其他角色，暂时为空
                plugins: {}, // 插件，暂时为空
                locales: ['zh-CN'] // 语言设置
            }

            console.log(`[Test_Debug] 构建 args 参数:`, {
                char_id: args.char_id,
                UserCharname: args.UserCharname,
                Charname: args.Charname,
                chat_log_length: args.chat_log.length
            })

            // 使用完整的 prompt 构建机制，detail_level = 3
            const builtPrompt = await GetPrompt(args, 3)

            console.log(`[Test_Debug] 构建的 prompt 结构:`, {
                text_count: builtPrompt.text?.length || 0,
                additional_chat_log_count: builtPrompt.additional_chat_log?.length || 0
            })

            // 添加构建的系统提示词
            if (builtPrompt.text && builtPrompt.text.length > 0) {
                const systemContent = builtPrompt.text
                    .sort((a, b) => a.important - b.important)
                    .map(item => item.content)
                    .filter(Boolean)
                    .join('\n')

                if (systemContent) {
                    messages.push({
                        role: 'system',
                        content: systemContent
                    })
                    console.log(`[Test_Debug] 添加系统提示词，长度: ${systemContent.length}`)
                }
            }

            // 添加构建的额外聊天日志
            if (builtPrompt.additional_chat_log && builtPrompt.additional_chat_log.length > 0) {
                for (const entry of builtPrompt.additional_chat_log) {
                    messages.push({
                        role: entry.role === 'user' ? 'user' : entry.role === 'system' ? 'system' : 'assistant',
                        content: entry.content
                    })
                }
                console.log(`[Test_Debug] 添加额外聊天日志，条数: ${builtPrompt.additional_chat_log.length}`)
            }

            // 添加原始聊天历史
            const chatLog = margeStructPromptChatLog(prompt_struct)
            for (const entry of chatLog) {
                messages.push({
                    role: entry.role === 'user' ? 'user' : entry.role === 'system' ? 'system' : 'assistant',
                    content: entry.content
                })
            }

            console.log(`[Test_Debug] 最终消息数组长度: ${messages.length}`)
            return messages

        } catch (error) {
            console.error(`[Test_Debug] prompt 构建失败，回退到基础实现:`, error.message)

            // 回退到原有的基础实现
            const systemPrompt = structPromptToSingleNoChatLog(prompt_struct)
            if (systemPrompt) {
                messages.push({
                    role: 'system',
                    content: systemPrompt
                })
            }

            const chatLog = margeStructPromptChatLog(prompt_struct)
            for (const entry of chatLog) {
                messages.push({
                    role: entry.role === 'user' ? 'user' : entry.role === 'system' ? 'system' : 'assistant',
                    content: entry.content
                })
            }

            return messages
        }
    }
}

async function testPromptBuilding() {
    console.log('🧪 开始测试 AIS_Mgr prompt 构建修复...')

    const manager = new TestAISourcesManager()
    
    // 创建测试用的 prompt_struct
    const testPromptStruct = {
        char_id: 'gentian',
        UserCharname: '测试用户',
        ReplyToCharname: '测试用户',
        Charname: '龙胆',
        chat_log: [
            {
                name: '测试用户',
                role: 'user',
                content: '你好，龙胆！'
            },
            {
                name: '龙胆',
                role: 'assistant',
                content: '你好！我是龙胆·阿芙萝黛蒂，很高兴见到你！'
            },
            {
                name: '测试用户',
                role: 'user',
                content: '请帮我写一个简单的程序'
            }
        ]
    }
    
    try {
        console.log('📝 测试 prompt_struct:', {
            char_id: testPromptStruct.char_id,
            UserCharname: testPromptStruct.UserCharname,
            Charname: testPromptStruct.Charname,
            chat_log_length: testPromptStruct.chat_log.length
        })
        
        // 测试 convertStructToMessages 方法
        console.log('\n🔄 调用 convertStructToMessages...')
        const messages = await manager.convertStructToMessages(testPromptStruct)
        
        console.log('\n✅ 成功构建消息数组!')
        console.log(`📊 消息数量: ${messages.length}`)
        
        // 分析消息结构
        const systemMessages = messages.filter(m => m.role === 'system')
        const userMessages = messages.filter(m => m.role === 'user')
        const assistantMessages = messages.filter(m => m.role === 'assistant')
        
        console.log(`\n📋 消息分析:`)
        console.log(`- 系统消息: ${systemMessages.length} 条`)
        console.log(`- 用户消息: ${userMessages.length} 条`)
        console.log(`- 助手消息: ${assistantMessages.length} 条`)
        
        // 显示系统消息内容（前500字符）
        if (systemMessages.length > 0) {
            console.log(`\n🤖 系统消息内容预览:`)
            const systemContent = systemMessages[0].content
            console.log(`"${systemContent.substring(0, 500)}${systemContent.length > 500 ? '...' : ''}"`)
            console.log(`📏 系统消息总长度: ${systemContent.length} 字符`)
        }
        
        // 检查是否包含 prompt 文件夹的构建内容
        const hasPromptContent = systemMessages.some(msg => 
            msg.content.includes('龙胆') || 
            msg.content.includes('角色设定') ||
            msg.content.includes('扮演')
        )
        
        if (hasPromptContent) {
            console.log('\n✅ 检测到 prompt 文件夹构建的内容!')
        } else {
            console.log('\n⚠️  未检测到 prompt 文件夹构建的内容')
        }
        
        console.log('\n🎉 测试完成!')
        return true
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message)
        console.error('错误堆栈:', error.stack)
        return false
    }
}

// 运行测试
if (import.meta.main) {
    testPromptBuilding()
        .then(success => {
            if (success) {
                console.log('\n🎯 所有测试通过!')
                process.exit(0)
            } else {
                console.log('\n💥 测试失败!')
                process.exit(1)
            }
        })
        .catch(error => {
            console.error('\n💥 测试运行出错:', error)
            process.exit(1)
        })
}

export { testPromptBuilding }
