/**
 * 聊天日志类型定义
 * 基于 fount 平台的聊天日志接口重新实现
 */

/**
 * 文件附件类型
 * @typedef {Object} FileAttachment_t
 * @property {Buffer} buffer - 文件缓冲区
 * @property {string} name - 文件名
 * @property {string} mimeType - MIME 类型
 * @property {string} description - 文件描述
 * @property {number} size - 文件大小
 */

/**
 * 聊天日志扩展信息类型
 * @typedef {Object} ChatLogExtension_t
 * @property {string[][]} decodedQRCodes - 解码的二维码内容
 * @property {Object} metadata - 元数据
 * @property {string} platform - 平台信息
 * @property {string} messageId - 消息ID
 */

/**
 * 聊天日志上下文类型
 * @typedef {Object} ChatLogContext_t
 * @property {string} name - 发送者名称
 * @property {string} role - 角色 (user, assistant, system)
 * @property {string} content - 内容
 * @property {string[]} charVisibility - 角色可见性
 * @property {FileAttachment_t[]} files - 附件文件
 */

/**
 * 聊天日志条目基础类型
 * @typedef {Object} chatLogEntry_t
 * @property {string} name - 发送者名称
 * @property {string} role - 角色 (user, assistant, system)
 * @property {string} content - 消息内容
 * @property {Date} time - 时间戳
 * @property {FileAttachment_t[]} files - 附件文件列表
 * @property {ChatLogExtension_t} extension - 扩展信息
 * @property {ChatLogContext_t[]} logContextBefore - 前置上下文
 * @property {ChatLogContext_t[]} logContextAfter - 后置上下文
 * @property {string} messageId - 消息唯一标识
 * @property {string} platform - 平台标识
 * @property {string} channelId - 频道标识
 * @property {string} userId - 用户标识
 * @property {boolean} isEdited - 是否已编辑
 * @property {boolean} isDeleted - 是否已删除
 */

/**
 * 聊天会话类型
 * @typedef {Object} ChatSession_t
 * @property {string} sessionId - 会话ID
 * @property {string} chatName - 聊天名称
 * @property {string} platform - 平台
 * @property {string} channelId - 频道ID
 * @property {chatLogEntry_t[]} messages - 消息列表
 * @property {Date} createdAt - 创建时间
 * @property {Date} lastActivity - 最后活动时间
 * @property {Object} metadata - 元数据
 */

/**
 * 聊天统计类型
 * @typedef {Object} ChatStatistics_t
 * @property {number} totalMessages - 总消息数
 * @property {number} userMessages - 用户消息数
 * @property {number} botMessages - 机器人消息数
 * @property {number} systemMessages - 系统消息数
 * @property {number} totalTokens - 总令牌数
 * @property {Object} dailyStats - 每日统计
 * @property {Object} userStats - 用户统计
 */

/**
 * 消息过滤器类型
 * @typedef {Object} MessageFilter_t
 * @property {string} platform - 平台过滤
 * @property {string} userId - 用户ID过滤
 * @property {string} channelId - 频道ID过滤
 * @property {Date} startTime - 开始时间
 * @property {Date} endTime - 结束时间
 * @property {string} role - 角色过滤
 * @property {string} keyword - 关键词过滤
 * @property {boolean} hasFiles - 是否包含文件
 */

/**
 * 聊天日志管理器类型
 * @typedef {Object} ChatLogManager_t
 * @property {Function} addMessage - 添加消息
 * @property {Function} getMessage - 获取消息
 * @property {Function} getMessages - 获取消息列表
 * @property {Function} updateMessage - 更新消息
 * @property {Function} deleteMessage - 删除消息
 * @property {Function} getSession - 获取会话
 * @property {Function} createSession - 创建会话
 * @property {Function} getStatistics - 获取统计信息
 * @property {Function} search - 搜索消息
 * @property {Function} export - 导出聊天记录
 * @property {Function} import - 导入聊天记录
 */

/**
 * 支持的功能类型
 * @typedef {Object} SupportedFunctions_t
 * @property {boolean} markdown - 支持 Markdown
 * @property {boolean} mathjax - 支持 MathJax
 * @property {boolean} html - 支持 HTML
 * @property {boolean} unsafe_html - 支持不安全 HTML
 * @property {boolean} files - 支持文件
 * @property {boolean} add_message - 支持添加消息
 */

/**
 * 单部分提示词类型
 * @typedef {Object} single_part_prompt_t
 * @property {Object[]} text - 文本内容数组
 * @property {string} text[].content - 文本内容
 * @property {string} text[].description - 文本描述
 * @property {number} text[].important - 重要性级别
 * @property {chatLogEntry_t[]} additional_chat_log - 额外聊天日志
 * @property {Object} extension - 扩展信息
 */

/**
 * 其他角色提示词类型
 * @typedef {Object} other_chars_prompt_t
 * @property {string} name - 角色名称
 * @property {boolean} isActive - 是否活跃
 * @property {number} LastActive - 最后活跃时间
 * @property {Object[]} text - 文本内容数组
 * @property {chatLogEntry_t[]} additional_chat_log - 额外聊天日志
 * @property {Object} extension - 扩展信息
 */

/**
 * 提示词结构类型 (兼容 fount 平台)
 * @typedef {Object} prompt_struct_t
 * @property {string} char_id - 角色ID
 * @property {string} Charname - 角色名称
 * @property {(string|RegExp)[]} alternative_charnames - 备选角色名
 * @property {string} UserCharname - 用户角色名
 * @property {string} [ReplyToCharname] - 回复目标角色名 (fount兼容字段)
 * @property {single_part_prompt_t} user_prompt - 用户提示词
 * @property {single_part_prompt_t} char_prompt - 角色提示词
 * @property {Record<string, other_chars_prompt_t>} other_chars_prompt - 其他角色提示词
 * @property {single_part_prompt_t} world_prompt - 世界提示词
 * @property {Record<string, single_part_prompt_t>} plugin_prompts - 插件提示词
 * @property {chatLogEntry_t[]} chat_log - 聊天日志
 */

/**
 * 聊天回复请求类型
 * @typedef {Object} chatReplyRequest_t
 * @property {string} char_id - 角色ID
 * @property {Object} char - 角色对象
 * @property {Object} user - 用户对象
 * @property {Object} world - 世界对象
 * @property {Object} other_chars - 其他角色对象
 * @property {Object} plugins - 插件对象
 * @property {chatLogEntry_t[]} chat_log - 聊天日志
 * @property {string} UserCharname - 用户角色名
 * @property {string} ReplyToCharname - 回复目标角色名
 * @property {string} Charname - 角色名称
 */

export {}
