{"sfw": {"name": "日常对话模型", "type": "openai", "endpoint": "https://tbai.xin/v1/chat/completions", "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS", "model": "gemini-2.5-flash-preview-05-20", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 2000}, "enabled": true, "description": "用于日常对话的标准模型"}, "nsfw": {"name": "内容创作模型", "type": "openai", "endpoint": "https://pro.92.run/hf/v1/chat/completions", "apiKey": "w656153.", "model": "gemini-2.5-pro-preview-03-25", "parameters": {"temperature": 0.9, "top_p": 0.95, "max_tokens": 8192}, "enabled": true, "description": "处理创意内容的专用模型"}, "expert": {"name": "专家咨询模型", "type": "openai", "endpoint": "https://usapi.zhiyunai168.com/v1/messages", "apiKey": "sk-KFg0ca8E9lv5akatVEipnzZVutTV477brMXrsW3S63MqN5Ei", "model": "claude-sonnet-4-20250514", "parameters": {"temperature": 0.5, "top_p": 0.9, "max_tokens": 4000}, "enabled": true, "description": "用于专业问题和技术咨询的高级模型"}, "detail-thinking": {"name": "详细思考模型", "type": "openai", "endpoint": "https://tbai.xin/v1/chat/completions", "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS", "model": "gemini-2.5-pro-exp-03-25", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}, "enabled": true, "description": "用于详细思考和复杂推理的高级模型"}, "web-browse": {"name": "网页浏览模型", "type": "openai", "endpoint": "https://pro.92.run/hf/v1/chat/completions", "apiKey": "w656153.", "model": "gemini-2.5-pro-preview-05-06-search", "parameters": {"temperature": 0.6, "top_p": 0.8, "max_tokens": 4096}, "enabled": true, "description": "专为网页浏览和信息提取优化的模型"}, "logic": {"name": "快速逻辑模型", "type": "openai", "endpoint": "https://tbai.xin/v1/chat/completions", "apiKey": "sk-TzwldRypA2ZEza7OPwFcPe5jduzIarlg8F73VBkUQmBmucdS", "model": "gemini-2.5-flash-preview-05-20", "parameters": {"temperature": 0.1, "top_p": 0.5, "max_tokens": 1000}, "enabled": true, "description": "快速响应的逻辑判断模型"}, "gemini": {"name": "Gemini AI模型", "type": "gemini", "endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "apiKey": "", "model": "gemini-1.5-flash", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4000}, "enabled": false, "description": "Google Gemini AI模型，支持多模态输入"}, "local": {"name": "本地模型", "type": "local", "endpoint": "http://localhost:11434", "apiKey": "", "model": "llama2:7b", "parameters": {"temperature": 0.7, "top_p": 0.9, "repeat_penalty": 1.1}, "enabled": false, "description": "本地部署的开源模型"}}